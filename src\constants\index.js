import DoctorImg from "../../public/images/product/games/doctor.webp";
// import {useTranslations} from 'next-intl';
// const t = useTranslations('Acquisition');

export const learningScreenshots = [
  "/images/productModal/LearningScreenshots/2.webp",
  "/images/productModal/LearningScreenshots/3.webp",
  "/images/productModal/LearningScreenshots/4.webp",
  "/images/productModal/LearningScreenshots/6.webp",
  "/images/productModal/LearningScreenshots/1.webp",
  "/images/productModal/LearningScreenshots/5.webp",
];

export const gamesData = [
  {
    gameName: "Doctor Games for Kids",
    gameDesc: "Help kids enjoy daily bathroom routines with cute friends!",
    gameSkill: "Empathy & Compassion",
    interests: ["PRETEND PLAY", "CASUAL", "PUZZLE"],
    thumbnailImage: DoctorImg,
    heroSection: {
      title: "Bath",
      subtitle: "Help kids enjoy daily bathroom routines with cute friends!",
      downloadButtons: [
        {
          type: "appStore",
          url: "https://apps.apple.com/us/app/doctor-games-for-kids/id1506886061",
        },
        {
          type: "playStore",
          url: "https://play.google.com/store/apps/details?id=skidos.preschool.kids.doctor.game",
        },
        { type: "amazonStore", url: "https://www.amazon.com/gp/product/B0F2HQ8DFF" },
        { type: "webStore", url: "https://skidos.com/get-started/" },
      ],
      thumbnailImage: DoctorImg,
    },
    mainContent: {
      videoUrl: "https://www.youtube.com/watch?v=IkL0O5pTPls",
      aboutSection: {
        title: "About SKIDOS Doctor",
        features: [
          "Fun doctor game for kids with adorable little patients",
          "Treat flu, ear aches & tooth aches through interactive play",
          "Build empathy & compassion with role-playing scenarios",
          "Boost cognitive skills with engaging medical challenges",
        ],
        description:
          "SKIDOS Doctor Game lets kids explore a cheerful doctor clinic with 3 adorable patients, each needing care for common ailments like flu, earaches, toothaches, and minor injuries. As one of the most engaging doctor games for kids online, this doctor simulation game allows children to play through fun scenes - cleaning wounds, taking X-rays, brushing teeth, and using real-life tools like thermometers and ear drops. It's a delightful mix of pretend play and health awareness, wrapped in colorful animations and kid-friendly storytelling, making it a standout among medical games online. Parents and children love this fun doctor role play app for its simplicity, educational value, and imaginative fun!",
      },
    },
    learningSection: {
      title: "What Kids Learn?",
      description:
        "SKIDOS Doctor Game helps kids build cognitive thinking, empathy, creativity, and critical thinking through fun medical role-play. This hospital simulation game isn't just entertaining - kids learn to diagnose, treat, and care for patients while developing essential child development skills and also, enhancing fine motor skills. Educational activities are seamlessly woven into gameplay to support holistic development, making it one of the most thoughtfully designed doctor games for kids online.",
      skills: [
        { title: "Empathy & Compassion", color: "#FFE5E5" },
        { title: "High-Impact Cognitive Skills", color: "#E5F3FF" },
        { title: "Health Awareness & Hygiene", color: "#E5FFE5" },
      ],
    },
    ageSection: {
      title: "Perfect for Kids Aged 3-8",
      description:
        "This kids doctor app is ideal for kids aged 3 to 8 who love learning through pretend play and want to explore the world of healthcare in a safe learning environment. It's especially great for preschoolers and early learners developing essential life skills through interactive role-play. As one of the most engaging doctor games for kids online, it offers a playful yet educational experience that nurtures curiosity and empathy.",
    },
    screenshots: [
      "/images/productModal/Doctor/doctor1.webp",
      "/images/productModal/Doctor/doctor2.webp",
      "/images/productModal/Doctor/doctor3.webp",
      "/images/productModal/Doctor/doctor4.webp",
    ],
    testimonials: [
      {
        author: "Rihanna's Mom 1",
        ageDescription: " Mother of  6-8 year old",
        rating: "4",
        content:
          "My child loves being the doctor in this game, it's playful, interactive, and educational at the same time. Great way to teach empathy, problem-solving, and basic medical concepts. Highly recommend for curious little minds!",
      },
      {
        author: "Rihanna's Mom 2",
        ageDescription: " Mother of  6-8 year old",
        rating: "2",
        content:
          "My child loves being the doctor in this game, it's playful, interactive, and educational at the same time. Great way to teach empathy, problem-solving, and basic medical concepts. Highly recommend for curious little minds!",
      },
    ],
    vsps: [
      { title: "Safe Learning Environment", thumbnailImg: "imageUrl" },
      { title: "Role-Playing with Educational Impact", thumbnailImg: "imageUrl" },
      { title: "Builds Critical Life Skills Through Play", thumbnailImg: "imageUrl" },
    ],
    faqs: [
      {
        question: "What age group is this game for?",
        answer: "This game is designed for kids aged 3-8 years old.",
      },
      {
        question: "Is this game educational?",
        answer:
          "Yes, it teaches empathy, healthcare awareness, and cognitive skills through interactive play.",
      },
      {
        question: "Does this game require internet?",
        answer: "No, once downloaded, the game can be played offline.",
      },
    ],
    discoverMOreFunGames: [
      {
        gameName: "Doctor Games for Kids",
        gameDesc: "Help kids enjoy daily bathroom routines with cute friends!",
        gameSkill: "Empathy & Compassion",
        thumbnailImage: DoctorImg,
      },
    ],
  },
];

export const themeFilters = [
  "All",
  "Action",
  "Casual",
  "Creative",
  "Pretend Play",
  "Racing",
  "Music",
  "Puzzle",
  "Sports",
  "Tracing",
  "Learn To Read",
  "Math",
  "Fitness",
  "Emotional Wellbeing",
];

export const themeCardsRow1 = [
  {
    themeName: "Action",
    themeDesc: "Thrilling action games to boost quick thinking",
    themeImg: "/images/action.webp",
  },
  {
    themeName: "Casual",
    themeDesc: "Relaxed casual games for fun and learning",
    themeImg: "/images/casual.webp",
  },
  {
    themeName: "Creative",
    themeDesc: "Innovative games to unleash your child's creativity",
    themeImg: "/images/creative.webp",
  },
  {
    themeName: "Music",
    themeDesc: "Melodious music games to nurture musical talent",
    themeImg: "/images/music.webp",
  },
  {
    themeName: "Puzzle",
    themeDesc: "Challenging puzzles to enhance problem-solving abilities",
    themeImg: "/images/puzzle.webp",
  },
  {
    themeName: "Pretend Play",
    themeDesc: "Imaginative play games to spark creativity",
    themeImg: "/images/rolePlay.webp",
  },
  {
    themeName: "Racing",
    themeDesc: "Exciting racing games to develop strategic skills",
    themeImg: "/images/racing.webp",
  },
];

export const themeCardsRow2 = [
  {
    themeName: "Sports",
    themeDesc: "Interactive sports games to encourage active learning",
    themeImg: "/images/sports.webp",
  },
  {
    themeName: "Math",
    themeDesc: "Engaging educational games to build strong numeracy skills",
    themeImg: "/images/math.webp",
  },
  {
    themeName: "Learn To Read",
    themeDesc: "Interactive and engaging activities to foster early reading",
    themeImg: "/images/learntoread.webp",
  },
  {
    themeName: "Tracing",
    themeDesc: "Fun tracing activities for writing practice",
    themeImg: "/images/tracing.webp",
  },
  {
    themeName: "Emotional Wellbeing",
    themeDesc: "Games to support emotional health and resilience",
    themeImg: "/images/emotional.webp",
  },
  {
    themeName: "Fitness",
    themeDesc: "Active games to promote healthy habits and fun exercises",
    themeImg: "/images/fitness.webp",
  },
];

export const aboutValues = [
  {
    title: "Collaboration",
    description: "Work together to achieve goals and build positive work relationships",
  },
  {
    title: "Customer Centricity",
    description: "Listen to all that customers say, act on what makes sense to the business",
  },
  {
    title: "Curiosity & Creativity",
    description: "The Skidos approach ensure learning is fun and engaging for kids",
  },
  {
    title: "Confident Learners",
    description: "We facilitate experiences that propel your kid’s confidence in learning",
  },
  {
    title: "Active Screen Time",
    description: "In a way that keeps them active and generate healthy habits",
  },
  {
    title: "Innovation",
    description: "Innovate to solve the biggest organizational challenges.",
  },
];

export const HomeStreet = [
  {
    starImg: "/images/4.5_star.png",
    content: "Testimionial1",
    description: "Testimionial1Parent",
  },
  {
    starImg: "/images/4.5_star.png",
    content: "Testimionial2",
    description: "Testimionial2Parent",
  },
  {
    starImg: "/images/4_star.png",
    content: "Testimionial3",
    description: "Testimionial3Parent",
  },
  {
    starImg: "/images/4.5_star.png",
    content: "Testimionial4",
    description: "Testimionial4Parent",
  },
  {
    starImg: "/images/4.5_star.png",
    content: "Testimionial5",
    description: "Testimionial5Parent",
  },
];

export const LandingReview = [
  {
    starImg: "/images/4.5_star.png",
    content:
      "It's the best doctor game for little ones and it's super good at making learning fun. My 3 year old is getting better at their ABCs and maths while pretending to be a dentist. They're also learning how to take care of their teeth and brush properly. Great job, developers!",
    description: "Lucia, Mother of 3 year old",
  },
  {
    starImg: "/images/4.5_star.png",
    content:
      "A fantastic and engaging game for children. They include coding lessons, which is excellent. My 7 year old loves this, and especially enjoyed being able to select puzzles and challenges within her most beloved topics of interest. Highly recommended!",
    description: "Jody, Father of 7 year old",
  },
  {
    starImg: "/images/4_star.png",
    content:
      "Kids in my school loves to play this game a lot. Further more it enhanced their math. Cool. Need more game like this",
    description: "Ravinder Saxena, Father of 5 year old",
  },
  {
    starImg: "/images/4.5_star.png",
    content:
      "My 5 years old loved this pet care game. This virtual game for toddlers is a wonderful preschool learning game. She had so much fun caring for the pets along with learning to trace letters and numbers! 5 stars!",
    description: "Milikatar, Father of 5 year old",
  },
  {
    starImg: "/images/4.5_star.png",
    content:
      "I babysat my 4- and 6-year-old niece and nephew, and they loved this learning game. I liked that they learned independently, and it was safe for them. I found they offer more such games, so babysitting will be fun!",
    description: "CattMae, Mother of 4 and 6",
  },
];

export const ArticleMedia = [
  {
    imageUrl: "/images/article/article1.png",
    link: "https://www.holoniq.com/notes/holoniq-nordic-baltic-edtech-50",
    description: "SKIDOS – Selected as a part of the 2020 HolonIQ Nordic-Baltic EdTech 50",
  },
  {
    imageUrl: "/images/article/article2.png",
    link: "https://www.siliconrepublic.com/start-ups/copenhagen-startups-2018",
    description: "14 fast-moving Copenhagen start-ups to watch in 2018",
  },
  {
    imageUrl: "/images/article/article3.png",
    link: "https://finans.dk/erhverv/ECE10818976/adi-flyttede-til-danmark-og-aabnede-et-spilfirma-nu-har-han-faaet-77-mio-kr-paa-et-aar/?ctxref=ext",
    description: "Adi flyttede til Danmark og åbnede et spilfirma: Har fået 7,7 mio. kr. på et år",
  },
  {
    imageUrl: "/images/article/article4.png",
    link: "https://www.eu-startups.com/2018/09/10-european-video-game-startups-to-watch-in-2018-and-beyond/",
    description: "10 European video game startups to watch in 2018",
  },
  {
    imageUrl: "/images/article/article5.png",
    link: "https://www.huffpost.com/entry/9-teams-raise-the-bar-in_b_8112634",
    description: "9 Teams Raise the Bar in Startupbootcamp Mobile 2015",
  },
  {
    imageUrl: "/images/article/article6.png",
    link: "https://techcrunch.com/2017/08/28/skidos/",
    description: "SKIDOS on TechCrunch",
  },
  {
    imageUrl: "/images/article/article7.png",
    link: "https://finans.dk/tech/**********/adi-vil-goere-boern-klogere-mens-de-spiller-spil-paa-tablets-og-telefoner/?ctxref=ext",
    description: "Adi vil gøre børn klogere, mens de spiller spil på tablets og telefoner",
  },
  {
    imageUrl: "/images/article/article8.png",
    link: "https://yourstory.com/2015/05/skidos-startupbootcamp-mobile-copenhagen",
    description:
      "SKIDOS is the first Indian startup to be part of Startupbootcamp Mobile in Copenhagen",
  },
];

export const inforPopupData = [
  {
    name: "Create Player Section",
    bannerImage: "/images/webGl/whyInfoBanner/playerProfile.webp",
    heading: "WhypopupHeading",
    popupContent: ["PopupItem1", "PopupItem2", "PopupItem3"],
  },
  {
    name: "Personalise Player Section",
    bannerImage: "/images/webGl/whyInfoBanner/childExperience.webp",
    heading: "WhypopupHeading",
    popupContent: ["PopupItem1", "PopupItem2"],
  },
  {
    name: "Enter Email Section",
    bannerImage: "/images/webGl/whyInfoBanner/yourEmail.webp",
    heading: "WhypopupHeading",
    // popupContent: [
    //   "To provide information about your child's progress via email, keeping you updated and involved in their learning journey.",
    //   "To create a unique ID that ensures secure access to your SKIDOS account.",
    //   "To facilitate effective communication between parents and our educators, allowing for easy exchanges of feedback, questions, and updates."
    // ]
    popupContent: ["PopupItem1", "PopupItem2", "PopupItem3"],
  },
];

export const carouselDataGetStarted = [
  {
    bannerImage: "/images/webGl/getStartedBanner/1.webp",
    key: "CarouselSubText1",
  },
  {
    bannerImage: "/images/webGl/getStartedBanner/2.webp",
    key: "CarouselSubText2",
  },
  {
    bannerImage: "/images/webGl/getStartedBanner/3.webp",
    key: "CarouselSubText3",
  },
];
