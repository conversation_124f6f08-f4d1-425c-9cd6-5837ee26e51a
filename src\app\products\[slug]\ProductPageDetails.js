/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
"use client";
import StoreIcons from "@/components/Footer/StoreIcons";
import YouTubeVideo from "@/components/YouTubeVideo";
import { generateGameSlug } from "@/utils/helperFunctions";
import Image from "next/image";
import { useRouter } from "next/navigation";
import styles from "./styles.module.css";

const ProductPageDetails = ({ game }) => {
  // Extract YouTube video ID from URL
  const getYouTubeVideoId = (url) => {
    if (!url) return null;
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
    return match ? match[1] : null;
  };

  const videoId = getYouTubeVideoId(game.mainContent.videoUrl);

  // Build store icons data from game.heroSection.downloadButtons
  const storeIconsData = [];

  if (game.heroSection?.downloadButtons) {
    game.heroSection.downloadButtons.forEach((button) => {
      let iconSrc = "";
      let altText = "";

      switch (button.type) {
        case "appStore":
          iconSrc = "/images/footer/socialIcons/Appstore.webp";
          altText = "Download on App Store";
          break;
        case "playStore":
          iconSrc = "/images/footer/socialIcons/Playstore.webp";
          altText = "Get it on Google Play";
          break;
        case "amazonStore":
          iconSrc = "/images/footer/socialIcons/Amazon.webp";
          altText = "Available on Amazon";
          break;
        case "webStore":
          iconSrc = "/images/footer/socialIcons/Web.webp";
          altText = "Play on Web";
          break;
      }

      if (iconSrc) {
        storeIconsData.push({
          src: iconSrc,
          alt: altText,
          onClick: () => window.open(button.url, "_blank", "noopener,noreferrer"),
        });
      }
    });
  }

  const router = useRouter();
  const handleGameClick = (game) => {
    const slug = generateGameSlug(game.gameDesc);
    router.push(`/products/${slug}`);
  };

  return (
    <div className={styles.productPageWrapper}>
      {/* Hero Section */}
      <div className={styles.heroSection}>
        <div className={styles.heroContent}>
          <div className={styles.heroLeft}>
            <h1 className={styles.gameTitle}>{game.heroSection?.title || game.gameName}</h1>
            <p className={styles.gameDescription}>{game.heroSection?.subtitle || game.gameDesc}</p>

            {/* Store Icons */}
            <StoreIcons icons={storeIconsData} />

            {/* Rating */}
            <div className={styles.ratingSection}>
              <div className={styles.ratingStars}>
                <span className={styles.ratingNumber}>
                  {game.heroSection?.rating?.stars || "4.3"}
                </span>
                <div className={styles.stars}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <span
                      key={star}
                      className={
                        star <= Math.floor(game.heroSection?.rating?.stars || 4.3)
                          ? styles.starFilled
                          : styles.starEmpty
                      }
                    >
                      ★
                    </span>
                  ))}
                </div>
                <span className={styles.reviewCount}>
                  {game.heroSection?.rating?.count || "36.3K"} Ratings
                </span>
              </div>
            </div>
          </div>

          <div className={styles.heroRight}>
            <div className={styles.gameImageWrapper}>
              <Image
                src={
                  game.heroSection?.thumbnailImage?.src ||
                  game.heroSection?.thumbnailImage ||
                  game.thumbnailImage?.src ||
                  game.thumbnailImage
                }
                alt={game.gameName}
                width={400}
                height={300}
                className={styles.gameImage}
                priority
              />
            </div>
          </div>
        </div>
      </div>

      <div className={styles.productWrapperInner}>
        {/* Video and About Section */}
        <div className={styles.videoAboutSection}>
          <div className={styles.videoContainer}>
            {videoId ? (
              <YouTubeVideo
                videoId={videoId}
                title={`${game.gameName} Gameplay Video`}
                width="100%"
                height="100%"
              />
            ) : (
              <div className={styles.videoPlaceholder}>
                <div className={styles.playButton}>
                  <div className={styles.playIcon}>▶</div>
                </div>
                <div className={styles.videoOverlay}>
                  <span>Video Not Available</span>
                </div>
              </div>
            )}
          </div>

          {/* About Game Section */}
          <div className={styles.aboutGameSection}>
            <h2 className={styles.aboutGameTitle}>
              {game.mainContent?.aboutSection?.title || "About the Game"}
            </h2>

            {/* About Game Points */}
            <ul className={styles.aboutGamePoints}>
              {(game.mainContent?.aboutSection?.features || []).map((point, index) => (
                <li key={index} className={styles.aboutGamePoint}>
                  {point}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* About Game Description */}
        <div className={styles.aboutGameDescription}>
          <p className={styles.descriptionText}>{game.mainContent?.aboutSection?.description}</p>
        </div>

        {/* Learning Outcomes Section */}
        <div className={styles.learningSection}>
          <h2 className={styles.learningSectionTitle}>{game.learningSection?.title}</h2>

          {/* Learning Description */}
          <p className={styles.learningDescription}>{game.learningSection?.description}</p>

          <div className={styles.learningOutcomes}>
            {(game.learningSection?.skills || []).map((skill, index) => (
              <div
                key={index}
                className={styles.learningCard}
                style={{ backgroundColor: skill.color }}
              >
                <div className={styles.learningIconPlaceholder}>
                  <span>📚</span>
                </div>
                <h3 className={styles.learningCardTitle}>{skill.title}</h3>
              </div>
            ))}
          </div>
        </div>

        {/* Age Section */}
        <div className={styles.ageSection}>
          <h2 className={styles.ageSectionTitle}>{game.ageSection?.title}</h2>
          <p className={styles.ageSectionDescription}>{game.ageSection?.description}</p>
        </div>

        {/* Game Screenshots */}
        <div className={styles.screenshotsSection}>
          {game.gamePopinImages?.map((image, index) => (
            <div key={index} className={styles.screenshotWrapper}>
              <Image
                src={image}
                alt={`${game.gameName} screenshot ${index + 1}`}
                width={200}
                height={356}
                className={styles.screenshot}
              />
            </div>
          ))}
        </div>

        {/* Reviews Section */}
        <div className={styles.reviewsSection}>
          <h2 className={styles.reviewsSectionTitle}>Learning that Parents Recommend</h2>
          <div className={styles.reviewsGrid}>
            {game.testimonials.map((review, index) => (
              <div key={index} className={styles.reviewCard}>
                <div className={styles.stars}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <span
                      key={star}
                      className={
                        star <= Math.floor(review.rating) ? styles.starFilled : styles.starEmpty
                      }
                    >
                      ★
                    </span>
                  ))}
                </div>
                <p className={styles.reviewContent}>{review.content}</p>
                <div className={styles.reviewHeader}>
                  <h4 className={styles.reviewAuthor}>{review.author} </h4>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* VSPs Section */}
        <div className={styles.vspsSection}>
          <div className={styles.vspsGrid}>
            {game.vsps.map((vsp, index) => (
              <div key={index} className={styles.vspCard}>
                <div className={styles.vspIcon}>
                  <span>{vsp.icon}</span>
                </div>
                <h3 className={styles.vspTitle}>{vsp.title}</h3>
              </div>
            ))}
          </div>
        </div>

        <StoreIcons icons={storeIconsData} />

        {/* FAQs Section */}
        <div className={styles.faqsSection}>
          <h2 className={styles.faqsSectionTitle}>FAQ</h2>
          <div className={styles.faqsList}>
            {game.faqs.map((faq, index) => (
              <details key={index} className={styles.faqItem}>
                <summary className={styles.faqQuestion}>{faq.question}</summary>
                <div className={styles.faqAnswer}>
                  <p>{faq.answer}</p>
                </div>
              </details>
            ))}
          </div>
        </div>

        {/* Related Games Section */}
        <div className={styles.relatedGamesSection}>
          <h2 className={styles.relatedGamesSectionTitle}>Discover More Fun Games</h2>
          <section className={styles.gameCardsWrapper}>
            {(game.discoverMOreFunGames || []).map((game) => {
              return (
                <div
                  key={game.gameName}
                  onClick={() => handleGameClick(game)}
                  className={styles.productCards}
                  // ref={(el) => setRef(el)}
                >
                  <Image
                    src={game.thumbnailImage}
                    alt={game.gameName}
                    className={styles.gameCardsImg}
                  />
                  <p className={styles.gameName}>{game.gameName}</p>
                  <p className={styles.gameDescription}>{game.gameDesc}</p>
                  <div className={styles.gameSkillsWrapper}>
                    <p>{game.gameSkill}</p>
                  </div>
                  <button className={styles.gameCardBtn}>Learn More</button>
                </div>
              );
            })}
          </section>
        </div>
      </div>
    </div>
  );
};

export default ProductPageDetails;
