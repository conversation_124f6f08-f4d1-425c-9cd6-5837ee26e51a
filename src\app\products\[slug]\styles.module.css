/* Product Page Wrapper */
.productPageWrapper {
  font-family: var(--font-poppins);
  background: #ffffff;
}

.productWrapperInner {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 60px;
  text-align: center;
}

/* Hero Section */
.heroSection {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  padding: 20px 0;
  position: relative;
  overflow: hidden;
}

.heroContent {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 60px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.heroLeft {
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.gameTitle {
  font-size: 3rem;
  font-weight: 900;
  color: #ffffff;
  margin: 0;
  line-height: 1.2;
  font-family: var(--font-poppins);
}

.gameDescription {
  font-size: 1.2rem;
  color: #ffffff;
  margin: 0;
  line-height: 1.5;
  font-weight: 500;
  opacity: 0.9;
}

/* Store Icons Section */
.productPageStoreIcons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  max-width: 400px;
}

/* Rating Section */
.ratingSection {
  margin-top: 8px;
}

.ratingStars {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ratingNumber {
  font-size: 1.8rem;
  font-weight: 600;
  color: #ffffff;
}

.stars {
  display: flex;
  gap: 4px;
}

.starFilled {
  color: #FFD700;
  font-size: 1.4rem;
}

.starEmpty {
  color: rgba(255, 255, 255, 0.3);
  font-size: 1.4rem;
}

.reviewCount {
  font-size: 1rem;
  color: #ffffff;
  opacity: 0.8;
}

/* Hero Right */
.heroRight {
  display: flex;
  justify-content: center;
  align-items: center;
}

.gameImageWrapper {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.gameImage {
  width: 100%;
  height: 32rem;
  border-radius: 15px;
}

/* Video and About Section */
.videoAboutSection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  margin: 60px 0;
  align-items: flex-start;
}

.videoContainer {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  border-radius: 16px;
  overflow: hidden;
  background: #f8f9fa;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

/* About Game Section */
.aboutGameSection {
  padding: 0;
}

.aboutGameTitle {
  font-size: 2.2rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 32px 0;
  line-height: 1.3;
  text-align: left;
}

.aboutGamePoints {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.aboutGamePoint {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #636e72;
  position: relative;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #74b9ff;
  padding-left: 40px;
}

.aboutGamePoint::before {
  content: "✓";
  color: #74b9ff;
  font-weight: bold;
  font-size: 1.2rem;
  position: absolute;
  left: 16px;
  top: 16px;
}

.aboutGameDescription {
  margin: 60px 0;
  padding: 0;
}

.descriptionText {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #2d3436;
  margin: 0;
  text-align: justify;
}

.videoPlaceholder {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.playButton {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.playButton:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 1);
}

.playIcon {
  font-size: 24px;
  color: #333;
  margin-left: 4px;
}

.videoOverlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
  font-weight: 500;
}

/* Learning Section */
.learningSection {
  margin: 80px 0;
}

.learningSectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 32px 0;
  line-height: 1.3;
}

.learningDescription {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #636e72;
  margin-bottom: 48px;
  text-align: justify;
}

.learningOutcomes {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  margin-bottom: 48px;
}

.learningCard {
  text-align: center;
  padding: 32px 24px;
  border-radius: 16px;
  transition: transform 0.3s ease;
}

.learningCard:hover {
  transform: translateY(-4px);
}

.learningIconPlaceholder {
  width: 80px;
  height: 80px;
  background-color: rgba(116, 185, 255, 0.1);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 24px auto;
  font-size: 2rem;
}

.learningCardTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3436;
  margin: 0;
  line-height: 1.4;
}

/* Age Section */
.ageSection {
  margin: 80px 0;
}

.ageSectionTitle {
  font-size: 2.2rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 24px 0;
  line-height: 1.3;
}

.ageSectionDescription {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #636e72;
  margin: 0;
  text-align: justify;
}

/* Screenshots Section */
.screenshotsSection {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin: 80px 0;
}

.screenshotWrapper {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.screenshotWrapper:hover {
  transform: translateY(-4px);
}

.screenshot {
  width: 100%;
  height: auto;
  display: block;
}

/* Reviews Section */
.reviewsSection {
  margin: 80px 0;
}

.reviewsSectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 48px 0;
  line-height: 1.3;
  text-align: center;
}

.reviewsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
}

.reviewCard {
  background: #f8f9ff;
  border-radius: 16px;
  padding: 32px 24px;
  border: none;
  transition: transform 0.3s ease;
  text-align: left;
}

.reviewCard:hover {
  transform: translateY(-4px);
}

.stars {
  display: flex;
  gap: 4px;
  margin-bottom: 20px;
}

.starFilled {
  color: #FFD700;
  font-size: 1.2rem;
}

.starEmpty {
  color: #e0e0e0;
  font-size: 1.2rem;
}

.reviewContent {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #4a4a4a;
  margin: 0 0 20px 0;
  text-align: left;
}

.reviewHeader {
  margin-top: 20px;
}

.reviewAuthor {
  font-size: 0.9rem;
  font-weight: 600;
  color: #6b46c1;
  margin: 0;
}

/* VSPs Section */
.vspsSection {
  margin: 80px 0;
}

.vspsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
}

.vspCard {
  text-align: center;
  padding: 40px 24px;
  background: #e8f4fd;
  border-radius: 16px;
  border: none;
  transition: transform 0.3s ease;
}

.vspCard:hover {
  transform: translateY(-4px);
}

.vspIcon {
  width: 80px;
  height: 80px;
  background-color: #ffffff;
  border-radius: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 24px auto;
  font-size: 2.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.vspTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3436;
  margin: 0;
  line-height: 1.4;
}

/* FAQs Section */
.faqsSection {
  margin: 80px 0;
}

.faqsSectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 48px 0;
  line-height: 1.3;
  text-align: center;
}

.faqsList {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.faqItem {
  background: #f8f9fa;
  border-radius: 12px;
  border: none;
  overflow: hidden;
}

.faqQuestion {
  padding: 20px 24px;
  font-size: 1rem;
  font-weight: 500;
  color: #4a4a4a;
  cursor: pointer;
  transition: background-color 0.3s ease;
  list-style: none;
  position: relative;
  background-color: #f8f9fa;
  margin: 0;
}

.faqQuestion:hover {
  background-color: #e9ecef;
}

.faqQuestion::after {
  content: "▼";
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.8rem;
  color: #6c757d;
  transition: transform 0.3s ease;
}

.faqItem[open] .faqQuestion::after {
  transform: translateY(-50%) rotate(180deg);
}

.faqAnswer {
  padding: 0 24px 20px 24px;
  background-color: #f8f9fa;
}

.faqAnswer p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #4a4a4a;
  margin: 0;
}

/* Related Games Section */
.relatedGamesSection {
  margin: 80px 0;
}

.relatedGamesSectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3436;
  margin: 0 0 48px 0;
  line-height: 1.3;
  text-align: center;
}

.relatedGamesGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
}

.relatedGameCard {
  text-align: center;
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: transform 0.3s ease;
}

.relatedGameCard:hover {
  transform: translateY(-4px);
}

.relatedGameImageWrapper {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
}

.relatedGameImage {
  width: 100%;
  height: auto;
  display: block;
}

.relatedGameName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3436;
  margin: 0 0 20px 0;
  line-height: 1.3;
}

.relatedGameStoreIcons {
  display: flex;
  justify-content: center;
}

.relatedGameStoreIconsStyle {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  max-width: 200px;
}

.relatedGameDescription {
  font-size: 0.9rem;
  color: #636e72;
  margin: 12px 0;
  line-height: 1.4;
  text-align: center;
}

.relatedGameTags {
  display: flex;
  justify-content: center;
  margin: 12px 0;
}

.skillTag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

.learnMoreButton {
  background-color: #74b9ff;
  color: white;
  border: none;
  padding: 8px 24px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.learnMoreButton:hover {
  background-color: #0984e3;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .productWrapperInner {
    padding: 0 20px;
  }

  .heroSection {
    padding: 40px 0;
  }

  .heroContent {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
    padding: 0 20px;
  }

  .gameTitle {
    font-size: 2.2rem;
  }

  .gameDescription {
    font-size: 1.1rem;
  }

  .productPageStoreIcons {
    grid-template-columns: repeat(2, 1fr);
    max-width: 300px;
    margin: 0 auto;
  }

  .videoAboutSection {
    grid-template-columns: 1fr;
    gap: 40px;
    margin: 40px 0;
  }

  .aboutGameTitle {
    font-size: 1.8rem;
  }

  .learningSection {
    margin: 60px 0;
  }

  .learningSectionTitle {
    font-size: 2rem;
  }

  .learningOutcomes {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .ageSection {
    margin: 60px 0;
  }

  .ageSectionTitle {
    font-size: 1.8rem;
  }

  .screenshotsSection {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin: 60px 0;
  }

  .reviewsSection {
    margin: 60px 0;
  }

  .reviewsSectionTitle {
    font-size: 2rem;
  }

  .reviewsGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .reviewCard {
    padding: 24px 20px;
  }

  .vspsSection {
    margin: 60px 0;
  }

  .vspsGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .vspCard {
    padding: 32px 20px;
  }

  .faqsSection {
    margin: 60px 0;
  }

  .faqsSectionTitle {
    font-size: 2rem;
  }

  .faqQuestion {
    padding: 16px 20px;
    font-size: 0.95rem;
  }

  .faqAnswer {
    padding: 0 20px 16px 20px;
  }

  .relatedGamesSection {
    margin: 60px 0;
  }

  .relatedGamesSectionTitle {
    font-size: 2rem;
  }

  .relatedGamesGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .relatedGameStoreIconsStyle {
    grid-template-columns: repeat(2, 1fr);
    max-width: 150px;
  }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .productWrapperInner {
    padding: 0 40px;
  }

  .videoAboutSection {
    gap: 30px;
  }

  .aboutGameSection {
    min-width: 250px;
  }

  .videoPlaceholder {
    min-width: 250px;
    min-height: 300px;
  }

  .gameDescriptions {
    min-width: 250px;
  }

  .videoContainer {
    min-width: 250px;
    height: 300px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .productWrapperInner {
    padding: 0 15px;
  }

  .gameTitle {
    font-size: 1.8rem;
  }

  .heroSection {
    padding: 20px 15px;
  }

  .productPageStoreIcons {
    grid-template-columns: 1fr;
    max-width: 200px;
  }

  .videoAboutSection {
    gap: 20px;
  }

  .aboutGameTitle {
    font-size: 1.5rem;
  }

  .ageSectionTitle {
    font-size: 1.8rem;
  }

  .faqsSectionTitle {
    font-size: 1.8rem;
  }

  .videoPlaceholder {
    min-height: 300px;
  }
}



.gameCardsWrapper {
  display: grid;
  justify-items: center;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  font-family: var(--font-poppins);
  margin-bottom: 6rem;
}
.gameName {
  font-weight: 700;
  font-size: 1.5rem;
  margin: 0;
}
.gameDescription {
  font-weight: 400;
  font-size: 1.1rem;
  color: rgba(0, 0, 0, 0.5);
  margin: 0;
}
.gameSkillsWrapper {
  display: flex;
  gap: 5px;
}
.gameSkillsWrapper p {
  font-size: 0.9rem;
  background-color: #f7f7f7;
  padding: 0.5rem 1rem 0.5rem 1rem;
  border-radius: 2.5rem;
  border: 1px solid #e9e9e9;
}

.productCards {
  padding: 1rem;
  border-radius: 1rem;
  border: 1px solid rgba(217, 217, 217, 1);
  background-color: #ffffff;
  max-width: 350px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.15);
  /* transition: box-shadow 0.3s ease-in-out, transform 0.3s ease-in-out;  */
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.productCards:hover {
  box-shadow: 0 0 12px rgba(0, 0, 0, 0);
  transform: scale(0.98);
}
.gameCardsImg {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 20px;
}

.gameCardBtn {
  width: 100%;
  margin: 1rem 0;
  background-color: transparent;
  font-size: 1.2rem;
  color: black;
  border: 1px solid #b0b0b0;
  border-radius: 0.5rem;
  padding: 0.8rem 2rem;
  text-decoration: none;
  cursor: pointer;
}
.gameCardBtn:hover {
  color: #ffff;
  background-color: black;
  border: 1px solid black;
}